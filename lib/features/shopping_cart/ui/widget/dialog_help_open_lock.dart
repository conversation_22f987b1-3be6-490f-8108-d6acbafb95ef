import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:kitemite_app/core/common/style/app_colors.dart';
import 'package:kitemite_app/core/common/style/app_text_style.dart';
import 'package:kitemite_app/core/common/widgets/buttons/common_button.dart';

Future<bool?> showBypassConfirmDialogNew(
    BuildContext context, bool isSeller) async {
  return await showDialog<bool>(
    context: context,
    barrierDismissible: false,
    builder: (context) => _BypassConfirmDialog(),
  );
}

class _BypassConfirmDialog extends StatefulWidget {
  @override
  _BypassConfirmDialogState createState() => _BypassConfirmDialogState();
}

class _BypassConfirmDialogState extends State<_BypassConfirmDialog> {
  Timer? _timer;
  int _countdown = 60;

  @override
  void initState() {
    super.initState();
    _startCountdown();
  }

  void _startCountdown() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_countdown > 0) {
        setState(() {
          _countdown--;
        });
      } else {
        _timer?.cancel();
        if (mounted) {
          Navigator.of(context).pop(false);
        }
      }
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: EdgeInsets.all(24.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Title
            Text(
              '鍵に接続できない？',
              style: AppTextStyles.bold(18.sp, color: AppColors.textPrimary),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 24.h),

            // Instructions
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildInstructionStep('1.アプリの操作をせず、鍵の操作から1分以上放置する'),
                SizedBox(height: 12.h),
                _buildInstructionStep('2.再度、鍵を指で触る（鍵が開く動作）'),
                SizedBox(height: 12.h),
                _buildInstructionStep('3.アプリで接続ボタンを押す'),
              ],
            ),
            SizedBox(height: 32.h),

            // Buttons
            Row(
              children: [
                Expanded(
                  child: CommonButton(
                    onPressed: () => Navigator.of(context).pop(false),
                    backgroundColor: AppColors.grey200,
                    customTitle: Text(
                      '戻る (${_countdown}s)',
                      style: AppTextStyles.bold(15.sp,
                          color: AppColors.textPrimary),
                    ),
                  ),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: CommonButton(
                    text: 'このまま決済へ',
                    onPressed: () => Navigator.of(context).pop(true),
                    backgroundColor: AppColors.primary,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInstructionStep(String text) {
    return Text(
      text,
      style: AppTextStyles.regular(
        14.sp,
        color: AppColors.textPrimary,
        height: 1.5,
      ),
    );
  }
}
